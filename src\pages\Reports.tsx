import React, { useState, useEffect, useRef } from 'react';
import '../styles/Reports.css';
import '../styles/ScheduleManagement.css';
import { FaChartLine, FaStar, FaClock, FaCalendarCheck, FaGift, FaClipboardCheck, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import IconWrapper from '../components/IconWrapper';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { useChild } from '../contexts/ChildContext';
import { Database } from '../services/Database';
import { formatDateString } from '../utils/dateUtils';

// 定義任務接口
interface Task {
  id: string;
  title: string;
  description: string;
  duration: number;
  status: 'not_started' | 'in_progress' | 'pending_review' | 'completed';
  type: string;
  points: number;
  method?: string;
  startTime?: string;
  endTime?: string;
  priority: number;
  date: string;
  questType?: string;
  imageUrl?: string;
  parentScore?: number;
  earnedStars?: number;
  actualStartTime?: string; // 任務實際開始時間
  actualEndTime?: string; // 任務實際完成時間
  activityRewardMultiplier?: number; // 獎勵活動倍數
  rewardActivityId?: string; // 觸發的獎勵活動ID
  originalStars?: number; // 原始星星數（未套用獎勵倍數）
}

// 定義獎勵兌換記錄接口
interface RewardExchangeRecord {
  id: string;
  rewardId: string;
  rewardTitle: string;
  rewardDescription: string;
  rewardCategory: string;
  rewardImage: string;
  cost: number;
  redeemTime: string;
  redeemTimestamp: number;
  status: 'pending' | 'completed';
  completedTime?: string;
  completedTimestamp?: number;
}

// 定義獎勵兌換統計接口
interface RewardExchangeStats {
  title: string;
  count: number;
  category: string;
  image: string;
}

// 定義活動接口
interface Activity {
  id: string;
  title: string;
  type: 'reward' | 'reminder' | 'punishment'; // 獎勵活動、檢核點或懲罰活動
  startTime: string;
  endTime: string;
  date: string;
  description?: string; // 活動描述/檢核方法
  rewardMultiplier?: number;
  punishmentMultiplier?: number; // 懲罰倍數 (0.1-0.9倍)
  completionPercentage?: number; // 完成任務百分比條件 (0-100)
  imageUrl?: string;
  priority?: number;
  // 檢核點相關字段
  checkStatus?: 'not_checked' | 'completed'; // 檢核狀態
  earnedStars?: number; // 檢核獲得的星星數
  checkedTime?: string; // 檢核完成時間
}

// 定義成就獎勵記錄接口
interface AchievementReward {
  id: string;
  achievementId: string;
  prize: {
    id: string;
    name: string;
    color: string;
    icon: string;
  };
  timestamp: string;
  date: string;
}

// 定義睡前檢查表小項目接口
interface BedtimeSubItem {
  id: string;
  title: string;
  completed: boolean;
}

// 定義睡前檢查表主項目接口
interface BedtimeItem {
  id: string;
  title: string;
  icon: string;
  completed: boolean;
  subItems: BedtimeSubItem[];
}

// 定義睡前檢查表資料接口
interface BedtimeChecklistData {
  date: string;
  items: BedtimeItem[];
  status: 'pending' | 'completed' | 'under_review' | 'reviewed';
  completedCount: number;
  totalCount: number;
  starsEarned: number;
  maxStars: number;
  parentReviewedAt?: string;
}

// 定義睡前檢查表每日統計接口
interface BedtimeDailyStats {
  date: string;
  totalSubItems: number;
  completedSubItems: number;
  completionRate: number;
  isReviewed: boolean;
  subItemsDetails: {
    [subItemId: string]: {
      title: string;
      completed: boolean;
      mainItemTitle: string;
      mainItemIcon: string;
    };
  };
}

// 定義睡前檢查表週統計接口
interface BedtimeWeeklyStats {
  totalDays: number;
  reviewedDays: number;
  totalSubItems: number;
  completedSubItems: number;
  overallCompletionRate: number;
  dailyStats: BedtimeDailyStats[];
  subItemsSummary: {
    [subItemId: string]: {
      title: string;
      mainItemTitle: string;
      mainItemIcon: string;
      completedDays: number;
      totalDays: number;
      completionRate: number;
    };
  };
}

const Reports: React.FC = () => {
  const { t } = useLanguage();
  const { currentUser } = useAuth();
  const { selectedChild } = useChild();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 狀態變數
  const [selectedWeekOffset, setSelectedWeekOffset] = useState(0); // 0=本週, 1=前1週, 2=前2週, 3=前3週
  const [weeklyStars, setWeeklyStars] = useState(0);
  const [weeklyBonusStars, setWeeklyBonusStars] = useState(0); // 額外獎勵星星
  const [completedTasksCount, setCompletedTasksCount] = useState(0);
  const [totalStudyTime, setTotalStudyTime] = useState(0); // 以分鐘為單位
  const [isLoading, setIsLoading] = useState(true);
  const [weeklyRewardExchanges, setWeeklyRewardExchanges] = useState<RewardExchangeRecord[]>([]);
  const [rewardExchangeStats, setRewardExchangeStats] = useState<RewardExchangeStats[]>([]);
  const [weeklyReminders, setWeeklyReminders] = useState<Activity[]>([]);
  const [completedRemindersCount, setCompletedRemindersCount] = useState(0);
  const [weeklyVocabularyData, setWeeklyVocabularyData] = useState<{ date: string; accuracy: number; challenged: boolean }[]>([]);
  const [weeklyAchievementRewards, setWeeklyAchievementRewards] = useState<AchievementReward[]>([]);
  const [bedtimeWeeklyStats, setBedtimeWeeklyStats] = useState<BedtimeWeeklyStats | null>(null);

  // 數據緩存
  const [dataCache, setDataCache] = useState<Map<string, any>>(new Map());

  // 生成默認的七天單字挑戰數據
  const generateDefaultVocabularyData = (startDate: Date) => {
    const defaultData = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      defaultData.push({
        date: dateStr,
        accuracy: 0,
        challenged: false
      });
    }
    return defaultData;
  };

  // 獲取本週一的日期
  const getMonday = (date: Date): Date => {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 調整星期日
    return new Date(date.setDate(diff));
  };

  // 獲取指定週次的週一日期
  const getMondayByWeekOffset = (weekOffset: number): Date => {
    const today = new Date();
    const currentMonday = getMonday(new Date(today));
    const targetMonday = new Date(currentMonday);
    targetMonday.setDate(currentMonday.getDate() - (weekOffset * 7));
    return targetMonday;
  };

  // 獲取週次顯示文本
  const getWeekDisplayText = (weekOffset: number): string => {
    if (weekOffset === 0) return t('reports.currentWeek');
    return t('reports.weeksAgo', { weeks: weekOffset.toString() });
  };

  // 獲取週次日期範圍文本
  const getWeekDateRangeText = (weekOffset: number): string => {
    const monday = getMondayByWeekOffset(weekOffset);
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    const mondayStr = monday.toLocaleDateString('zh-TW', { month: 'numeric', day: 'numeric' });
    const sundayStr = sunday.toLocaleDateString('zh-TW', { month: 'numeric', day: 'numeric' });

    return `${mondayStr} - ${sundayStr}`;
  };

  // 格式化學習時間
  const formatStudyTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}${t('reports.hours')} ${mins}${t('reports.minutes')}`;
  };

  // 計算任務實際花費時間（分鐘）
  const calculateTaskDuration = (task: Task): number => {
    if (!task.actualStartTime || !task.actualEndTime) return 0;

    // 計算實際進行時間（分鐘）
    const [startHours, startMinutes] = task.actualStartTime.split(':').map(Number);
    const [endHours, endMinutes] = task.actualEndTime.split(':').map(Number);

    // 轉換為分鐘
    const startTimeInMinutes = startHours * 60 + startMinutes;
    const endTimeInMinutes = endHours * 60 + endMinutes;

    // 計算實際進行時間（考慮跨日情況）
    let actualDuration = endTimeInMinutes - startTimeInMinutes;
    if (actualDuration < 0) {
      // 如果是跨日，加上24小時
      actualDuration += 24 * 60;
    }

    return actualDuration;
  };

  // 獲取指定週次的所有任務數據
  useEffect(() => {
    const fetchWeeklyData = async () => {
      if (!currentUser || !selectedChild) {
        setIsLoading(false);
        return;
      }

      // 檢查緩存
      const cacheKey = `${selectedChild.id}-${selectedWeekOffset}`;
      if (dataCache.has(cacheKey)) {
        console.log(`使用緩存數據: 第${selectedWeekOffset}週`);
        const cachedData = dataCache.get(cacheKey);

        // 從緩存恢復所有狀態
        setWeeklyStars(cachedData.weeklyStars);
        setWeeklyBonusStars(cachedData.weeklyBonusStars);
        setCompletedTasksCount(cachedData.completedTasksCount);
        setTotalStudyTime(cachedData.totalStudyTime);
        setWeeklyReminders(cachedData.weeklyReminders);
        setCompletedRemindersCount(cachedData.completedRemindersCount);
        setTaskTypeProgress(cachedData.taskTypeProgress);
        setWeeklyRewardExchanges(cachedData.weeklyRewardExchanges);
        setRewardExchangeStats(cachedData.rewardExchangeStats);
        setWeeklyAchievementRewards(cachedData.weeklyAchievementRewards);
        // 確保單字挑戰數據包含完整的七天
        const monday = getMondayByWeekOffset(selectedWeekOffset);
        const vocabularyData = cachedData.weeklyVocabularyData && cachedData.weeklyVocabularyData.length === 7
          ? cachedData.weeklyVocabularyData
          : generateDefaultVocabularyData(monday);
        setWeeklyVocabularyData(vocabularyData);
        setBedtimeWeeklyStats(cachedData.bedtimeWeeklyStats || null);

        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // 獲取指定週次的週一日期
        const monday = getMondayByWeekOffset(selectedWeekOffset);
        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);

        // 如果是當前週，只查詢到今天；否則查詢整週
        const today = new Date();
        const endDate = selectedWeekOffset === 0 && sunday > today ? today : sunday;

        // 生成日期範圍
        const dateRange: string[] = [];
        const currentDate = new Date(monday);

        while (currentDate <= endDate) {
          dateRange.push(formatDateString(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }

        console.log(`獲取第${selectedWeekOffset}週的日期範圍:`, dateRange);

        // 獲取每一天的任務數據
        let totalStars = 0;
        let totalBonusStars = 0;
        let totalCompletedTasks = 0;
        let totalTime = 0;
        const allReminders: Activity[] = [];
        let totalCompletedReminders = 0;

        // 並行查詢所有日期的任務數據
        const taskPromises = dateRange.map(date =>
          Database.queryDocs<Task>('tasks', [], undefined, undefined, undefined, undefined, selectedChild.id, date)
            .then(tasks => tasks.filter(task => task.type !== 'extra'))
        );

        const allTasksResults = await Promise.all(taskPromises);

        // 初始化各類型任務計數（合併到主要數據處理中）
        const typeCounts: Record<string, { total: number; completed: number }> = {};
        taskTypes.forEach(type => {
          typeCounts[type.type] = { total: 0, completed: 0 };
        });

        // 處理所有任務數據
        allTasksResults.forEach((tasks, index) => {
          const date = dateRange[index];

          // 過濾已完成的任務
          const completedTasks = tasks.filter(task => task.status === 'completed');

          // 計算星星數
          const dateStars = completedTasks.reduce((sum, task) => sum + (task.earnedStars || 0), 0);
          totalStars += dateStars;

          // 計算完成的任務數
          totalCompletedTasks += completedTasks.length;

          // 計算實際花費時間
          const dateTime = completedTasks.reduce((sum, task) => sum + calculateTaskDuration(task), 0);
          totalTime += dateTime;

          // 同時計算各類型任務數量（避免重複查詢）
          tasks.forEach(task => {
            const taskType = task.type;
            if (typeCounts[taskType]) {
              typeCounts[taskType].total += 1;
              if (task.status === 'completed') {
                typeCounts[taskType].completed += 1;
              }
            }
          });
        });

        // 並行查詢所有日期的檢核點活動和額外獎勵星星
        const activityPromises = dateRange.map(date =>
          Database.queryDocs<Activity>('activities', [], undefined, undefined, undefined, undefined, selectedChild.id, date)
            .catch(error => {
              console.error(`獲取日期 ${date} 的檢核點失敗:`, error);
              return [];
            })
        );

        const bonusRewardsPromises = dateRange.map(date =>
          Database.getDoc<{ bonus: number }>('bonusRewards', date, undefined, selectedChild.id)
            .catch(error => {
              console.error(`獲取日期 ${date} 的額外獎勵星星失敗:`, error);
              return null;
            })
        );

        // 並行查詢所有日期的單字挑戰記錄
        const vocabularyPromises = dateRange.map(date =>
          Database.getDoc('vocabularyChallenge', date, undefined, selectedChild.id)
            .catch(error => {
              console.error(`獲取日期 ${date} 的單字挑戰記錄失敗:`, error);
              return null;
            })
        );

        const [allActivitiesResults, allBonusRewardsResults, allVocabularyResults] = await Promise.all([
          Promise.all(activityPromises),
          Promise.all(bonusRewardsPromises),
          Promise.all(vocabularyPromises)
        ]);

        // 處理所有檢核點數據
        allActivitiesResults.forEach((activities, index) => {
          const date = dateRange[index];
          const reminders = activities.filter(activity => activity.type === 'reminder');

          if (reminders.length > 0) {
            allReminders.push(...reminders);
            // 計算已完成的檢核點數量
            const completedReminders = reminders.filter(reminder => reminder.checkStatus === 'completed');
            totalCompletedReminders += completedReminders.length;
            console.log(`日期 ${date} 的檢核點: ${reminders.length}個，已完成: ${completedReminders.length}個`);
          }
        });

        // 處理所有額外獎勵星星數據
        allBonusRewardsResults.forEach((bonusRewardsDoc, index) => {
          const date = dateRange[index];
          if (bonusRewardsDoc && typeof bonusRewardsDoc.bonus === 'number') {
            totalBonusStars += bonusRewardsDoc.bonus;
            console.log(`日期 ${date} 的額外獎勵星星: ${bonusRewardsDoc.bonus}`);
          }
        });

        // 處理所有單字挑戰數據 - 固定顯示七天
        // 首先生成默認的七天數據
        const vocabularyData = generateDefaultVocabularyData(monday);

        // 然後用實際的挑戰數據覆蓋對應的天數
        dateRange.forEach((date, index) => {
          const vocabularyDoc = allVocabularyResults[index];
          if (vocabularyDoc && vocabularyDoc.challenged) {
            const accuracy = vocabularyDoc.correctCount !== undefined ?
              Math.round((vocabularyDoc.correctCount / 10) * 100) : 0; // 假設總共10題
            vocabularyData[index] = {
              date,
              accuracy,
              challenged: true
            };
          }
        });



        // 更新狀態
        // 計算任務類型進度百分比
        const progressData = taskTypes.map(type => {
          const counts = typeCounts[type.type];
          const progress = counts.total > 0 ? Math.round((counts.completed / counts.total) * 100) : 0;
          return {
            ...type,
            progress,
            total: counts.total,
            completed: counts.completed
          };
        });

        // 並行獲取獎勵兌換記錄、成就獎勵和睡前檢查表統計
        const [exchanges, achievementRewards, bedtimeStats] = await Promise.all([
          fetchWeeklyRewardExchanges(dateRange, selectedChild.id),
          fetchWeeklyAchievementRewards(selectedWeekOffset, selectedChild.id),
          fetchBedtimeChecklistStats(selectedWeekOffset, currentUser.uid, selectedChild.id)
        ]);

        // 計算獎勵兌換統計
        const stats = calculateRewardExchangeStats(exchanges);

        // 更新所有狀態
        setWeeklyStars(totalStars);
        setWeeklyBonusStars(totalBonusStars);
        setCompletedTasksCount(totalCompletedTasks);
        setTotalStudyTime(totalTime);
        setWeeklyReminders(allReminders);
        setCompletedRemindersCount(totalCompletedReminders);
        setTaskTypeProgress(progressData);
        setWeeklyRewardExchanges(exchanges);
        setRewardExchangeStats(stats);
        setWeeklyAchievementRewards(achievementRewards);
        setWeeklyVocabularyData(vocabularyData);
        setBedtimeWeeklyStats(bedtimeStats);

        // 緩存數據
        const cacheData = {
          weeklyStars: totalStars,
          weeklyBonusStars: totalBonusStars,
          completedTasksCount: totalCompletedTasks,
          totalStudyTime: totalTime,
          weeklyReminders: allReminders,
          completedRemindersCount: totalCompletedReminders,
          taskTypeProgress: progressData,
          weeklyRewardExchanges: exchanges,
          rewardExchangeStats: stats,
          weeklyAchievementRewards: achievementRewards,
          weeklyVocabularyData: vocabularyData,
          bedtimeWeeklyStats: bedtimeStats
        };

        setDataCache(prev => new Map(prev.set(cacheKey, cacheData)));

        console.log(`第${selectedWeekOffset}週數據: 星星數=${totalStars}, 額外獎勵星星=${totalBonusStars}, 完成任務數=${totalCompletedTasks}, 學習時長=${totalTime}分鐘, 檢核點=${allReminders.length}個，已完成=${totalCompletedReminders}個`);
      } catch (error) {
        console.error('獲取週報告數據失敗:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWeeklyData();
  }, [currentUser, selectedChild, selectedWeekOffset]);

  // 定義任務類型及其顏色
  const taskTypes = [
    { type: 'homework', name: '作業', color: '#4caf50' },
    { type: 'exercise', name: '運動', color: '#2196f3' },
    { type: 'housework', name: '家務', color: '#ff9800' },
    { type: 'study', name: '學習', color: '#9c27b0' },
    { type: 'habit', name: '好習慣', color: '#795548' },
    { type: 'extra', name: '額外任務', color: '#f44336' }
  ];

  // 各類型任務進度狀態
  const [taskTypeProgress, setTaskTypeProgress] = useState<{ type: string; name: string; progress: number; color: string; total: number; completed: number }[]>([]);

  // 獲取指定週次的獎勵兌換記錄
  const fetchWeeklyRewardExchanges = async (dateRange: string[], childId: string) => {
    try {
      console.log(`開始獲取第${selectedWeekOffset}週的獎勵兌換記錄`);
      const allExchanges: RewardExchangeRecord[] = [];

      // 並行查詢所有日期的獎勵兌換記錄
      const exchangePromises = dateRange.map(date =>
        Database.queryDocs<RewardExchangeRecord>('rewardHistory', [
          ['status', '==', 'completed']
        ], undefined, undefined, undefined, undefined, childId, date)
          .catch(error => {
            console.error(`查詢日期 ${date} 的獎勵兌換記錄失敗:`, error);
            return [];
          })
      );

      const allExchangeResults = await Promise.all(exchangePromises);

      // 合併所有兌換記錄
      allExchangeResults.forEach(exchanges => {
        if (exchanges.length > 0) {
          allExchanges.push(...exchanges);
        }
      });

      console.log(`獲取到 ${allExchanges.length} 筆第${selectedWeekOffset}週獎勵兌換記錄`);
      return allExchanges;
    } catch (error) {
      console.error(`獲取第${selectedWeekOffset}週獎勵兌換記錄失敗:`, error);
      return [];
    }
  };

  // 獲取指定週次的成就獎勵記錄（只查詢週日，因為成就獎勵只在週日產生）
  const fetchWeeklyAchievementRewards = async (weekOffset: number, childId: string) => {
    try {
      console.log(`開始獲取第${weekOffset}週的成就獎勵記錄`);

      // 找到指定週次的週日日期（成就獎勵只在週日產生）
      const monday = getMondayByWeekOffset(weekOffset);
      const sunday = new Date(monday);
      sunday.setDate(sunday.getDate() + 6);
      const sundayDate = formatDateString(sunday);

      console.log(`查詢週日 ${sundayDate} 的成就獎勵記錄`);

      // 只查詢週日的成就獎勵記錄
      const rewards = await Database.queryDocs<AchievementReward>('achievements', [], undefined, undefined, undefined, undefined, childId, sundayDate);

      console.log(`獲取到 ${rewards.length} 筆第${weekOffset}週成就獎勵記錄`);
      return rewards;
    } catch (error) {
      console.error(`獲取第${weekOffset}週成就獎勵記錄失敗:`, error);
      return [];
    }
  };

  // 獲取指定週次的睡前檢查表統計資料
  const fetchBedtimeChecklistStats = async (weekOffset: number, userId: string, childId: string): Promise<BedtimeWeeklyStats> => {
    try {
      // 獲取指定週次的週一日期
      const monday = getMondayByWeekOffset(weekOffset);

      // 生成完整的一週日期範圍（週一到週日）
      const fullWeekDateRange: string[] = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(monday);
        date.setDate(monday.getDate() + i);
        fullWeekDateRange.push(formatDateString(date));
      }

      console.log(`開始獲取睡前檢查表統計資料，完整週期: ${fullWeekDateRange.join(', ')}`);

      // 並行查詢所有日期的睡前檢查表資料
      const bedtimePromises = fullWeekDateRange.map(date =>
        Database.getDoc('bedtimeChecklist', 'checklist', userId, childId, date)
          .catch(error => {
            console.error(`獲取日期 ${date} 的睡前檢查表失敗:`, error);
            return null;
          })
      );

      const allBedtimeResults = await Promise.all(bedtimePromises);

      // 初始化統計資料
      const dailyStats: BedtimeDailyStats[] = [];
      const subItemsSummary: { [subItemId: string]: any } = {};
      let totalSubItems = 0;
      let completedSubItems = 0;
      let reviewedDays = 0;

      // 處理每一天的資料
      fullWeekDateRange.forEach((date, index) => {
        const bedtimeDoc = allBedtimeResults[index];
        let dayStats: BedtimeDailyStats;

        if (bedtimeDoc && bedtimeDoc.checklistData) {
          const checklistData = bedtimeDoc.checklistData as BedtimeChecklistData;
          const isReviewed = checklistData.status === 'reviewed' && !!checklistData.parentReviewedAt;

          if (isReviewed) {
            reviewedDays++;
          }

          // 計算當日小項目統計
          let dayTotalSubItems = 0;
          let dayCompletedSubItems = 0;
          const subItemsDetails: { [subItemId: string]: any } = {};

          checklistData.items.forEach(item => {
            item.subItems.forEach(subItem => {
              dayTotalSubItems++;
              if (subItem.completed) {
                dayCompletedSubItems++;
              }

              // 記錄小項目詳細資訊
              subItemsDetails[subItem.id] = {
                title: subItem.title,
                completed: subItem.completed,
                mainItemTitle: item.title,
                mainItemIcon: item.icon
              };

              // 累計到總統計中
              if (!subItemsSummary[subItem.id]) {
                subItemsSummary[subItem.id] = {
                  title: subItem.title,
                  mainItemTitle: item.title,
                  mainItemIcon: item.icon,
                  completedDays: 0,
                  totalDays: 0,
                  completionRate: 0
                };
              }
              subItemsSummary[subItem.id].totalDays++;
              if (subItem.completed) {
                subItemsSummary[subItem.id].completedDays++;
              }
            });
          });

          totalSubItems += dayTotalSubItems;
          completedSubItems += dayCompletedSubItems;

          dayStats = {
            date,
            totalSubItems: dayTotalSubItems,
            completedSubItems: dayCompletedSubItems,
            completionRate: dayTotalSubItems > 0 ? Math.round((dayCompletedSubItems / dayTotalSubItems) * 100) : 0,
            isReviewed,
            subItemsDetails
          };
        } else {
          // 沒有資料的日期 - 顯示預設的睡前檢查表結構
          const defaultSubItemsDetails: { [subItemId: string]: any } = {};

          // 使用預設的睡前檢查表結構來初始化小項目統計
          const defaultItems = [
            { id: 'organize-bag', title: '整理書包', icon: '🎒', subItems: [
              { id: 'check-homework', title: '檢查作業是否完成' },
              { id: 'pack-textbooks', title: '收拾明天需要的課本' },
              { id: 'pack-stationery', title: '整理文具用品' },
              { id: 'check-notices', title: '檢查聯絡簿和通知單' }
            ]},
            { id: 'tidy-toys', title: '收拾玩具', icon: '🧸', subItems: [
              { id: 'put-toys-back', title: '將玩具放回原位' },
              { id: 'organize-lego', title: '確認沒有亂丟東西在地上或桌上' },
              { id: 'clean-play-area', title: '保持自己座位附近整潔' }
            ]},
            { id: 'share-daily-thoughts', title: '分享今日心得', icon: '💭', subItems: [
              { id: 'share-happy-moment', title: '分享今天最開心的事' },
              { id: 'share-learning', title: '說說今天學到了什麼' },
              { id: 'share-gratitude', title: '感謝今天幫助過我的人' }
            ]},
            { id: 'self-reflection', title: '自我反省', icon: '🤔', subItems: [
              { id: 'maintain-politeness', title: '保持禮貌好習慣' },
              { id: 'no-fighting', title: '沒有跟同學吵架' },
              { id: 'no-punishment', title: '沒有被罵或處罰' },
              { id: 'no-tantrums', title: '沒有哭鬧或鬧脾氣' }
            ]},
            { id: 'bedtime-routine', title: '睡前準備', icon: '🌙', subItems: [
              { id: 'brush-teeth', title: '睡前刷牙' },
              { id: 'drink-water', title: '記得上廁所' },
              { id: 'say-goodnight', title: '向家人道晚安' },
              { id: 'set-alarm', title: '設定明天的鬧鐘' }
            ]}
          ];

          // 初始化小項目統計（即使沒有資料也要計入總天數）
          defaultItems.forEach(item => {
            item.subItems.forEach(subItem => {
              if (!subItemsSummary[subItem.id]) {
                subItemsSummary[subItem.id] = {
                  title: subItem.title,
                  mainItemTitle: item.title,
                  mainItemIcon: item.icon,
                  completedDays: 0,
                  totalDays: 0,
                  completionRate: 0
                };
              }
              subItemsSummary[subItem.id].totalDays++;
            });
          });

          dayStats = {
            date,
            totalSubItems: 0,
            completedSubItems: 0,
            completionRate: 0,
            isReviewed: false,
            subItemsDetails: defaultSubItemsDetails
          };
        }

        dailyStats.push(dayStats);
      });

      // 計算各小項目的完成率
      Object.keys(subItemsSummary).forEach(subItemId => {
        const summary = subItemsSummary[subItemId];
        summary.completionRate = summary.totalDays > 0
          ? Math.round((summary.completedDays / summary.totalDays) * 100)
          : 0;
      });

      const weeklyStats: BedtimeWeeklyStats = {
        totalDays: 7, // 固定為7天
        reviewedDays,
        totalSubItems,
        completedSubItems,
        overallCompletionRate: totalSubItems > 0 ? Math.round((completedSubItems / totalSubItems) * 100) : 0,
        dailyStats,
        subItemsSummary
      };

      console.log(`睡前檢查表週統計完成:`, weeklyStats);
      return weeklyStats;
    } catch (error) {
      console.error('獲取睡前檢查表統計資料失敗:', error);
      return {
        totalDays: 7, // 固定為7天
        reviewedDays: 0,
        totalSubItems: 0,
        completedSubItems: 0,
        overallCompletionRate: 0,
        dailyStats: [],
        subItemsSummary: {}
      };
    }
  };

  // 計算獎勵兌換統計
  const calculateRewardExchangeStats = (exchanges: RewardExchangeRecord[]): RewardExchangeStats[] => {
    // 使用 Map 來統計每種獎勵的兌換次數
    const statsMap = new Map<string, RewardExchangeStats>();

    exchanges.forEach(exchange => {
      const { rewardTitle, rewardCategory, rewardImage } = exchange;

      if (statsMap.has(rewardTitle)) {
        // 如果已經有這種獎勵的統計，增加計數
        const existingStat = statsMap.get(rewardTitle)!;
        existingStat.count += 1;
      } else {
        // 如果這是新的獎勵類型，創建新的統計項
        statsMap.set(rewardTitle, {
          title: rewardTitle,
          count: 1,
          category: rewardCategory,
          image: rewardImage
        });
      }
    });

    // 將 Map 轉換為數組並按照兌換次數降序排序
    return Array.from(statsMap.values()).sort((a, b) => b.count - a.count);
  };



  const weeklyProgress = {
    stars: weeklyStars,
    bonusStars: weeklyBonusStars,
    completedTasks: completedTasksCount,
    studyTime: formatStudyTime(totalStudyTime)
  };

  // 不再使用預設的成就列表

  // 根據獎勵類別取得顏色
  const getRewardCategoryColor = (category: string): string => {
    switch (category.toLowerCase()) {
      case 'time':
        return '#4caf50'; // 綠色
      case 'activity':
        return '#2196f3'; // 藍色
      case 'physical':
        return '#ff9800'; // 橙色
      case 'special':
        return '#9c27b0'; // 紫色
      case 'virtual':
        return '#f44336'; // 紅色
      default:
        return '#757575'; // 灰色
    }
  };

  return (
      <div className="reports page-container">
        <div className="reports-header">
          <div className="header-content">
            <IconWrapper icon={FaChartLine} className="reports-icon" />
            <h1>{t('reports.title')}</h1>
          </div>

          {/* 週次選擇器 */}
          <div className="week-selector">
            <button
              className="week-nav-btn"
              onClick={() => setSelectedWeekOffset(Math.min(3, selectedWeekOffset + 1))}
              disabled={selectedWeekOffset >= 3 || isLoading}
            >
              <IconWrapper icon={FaChevronLeft} size={14} />
            </button>

            <div className="week-info">
              <div className="week-title">
                {getWeekDisplayText(selectedWeekOffset)}
                {isLoading && <span className="loading-indicator"> ⏳</span>}
              </div>
              <div className="week-date-range">{getWeekDateRangeText(selectedWeekOffset)}</div>
            </div>

            <button
              className="week-nav-btn"
              onClick={() => setSelectedWeekOffset(Math.max(0, selectedWeekOffset - 1))}
              disabled={selectedWeekOffset <= 0 || isLoading}
            >
              <IconWrapper icon={FaChevronRight} size={14} />
            </button>
          </div>
        </div>

        <div className="reports-content">
          {isLoading ? (
            <div className="loading-container">
              <p>{t('common.loading')}</p>
            </div>
          ) : (
            <>
              <section className="weekly-stats">
                <h2>{t('reports.weeklyData')}</h2>
                <div className="stats-grid">
                  <div className="stat-card">
                    <IconWrapper icon={FaStar} className="stat-icon" color="#FFD700" />
                    <div className="stat-info">
                      <span className="stat-value">{weeklyProgress.stars}</span>
                      <span className="stat-label">{t('reports.earnedStars')}</span>
                    </div>
                  </div>

                  <div className="stat-card">
                    <IconWrapper icon={FaStar} className="stat-icon" color="#FF6B6B" />
                    <div className="stat-info">
                      <span className="stat-value">{weeklyProgress.bonusStars}</span>
                      <span className="stat-label">{t('reports.bonusStars')}</span>
                    </div>
                  </div>

                  <div className="stat-card">
                    <IconWrapper icon={FaCalendarCheck} className="stat-icon" color="#4CAF50" />
                    <div className="stat-info">
                      <span className="stat-value">{weeklyProgress.completedTasks}</span>
                      <span className="stat-label">{t('reports.completedTasks')}</span>
                    </div>
                  </div>

                  <div className="stat-card">
                    <IconWrapper icon={FaClock} className="stat-icon" color="#2196F3" />
                    <div className="stat-info">
                      <span className="stat-value">{weeklyProgress.studyTime}</span>
                      <span className="stat-label">{t('reports.studyTime')}</span>
                    </div>
                  </div>

                  {weeklyReminders.length > 0 && (
                    <div className="stat-card">
                      <IconWrapper icon={FaClipboardCheck} className="stat-icon" color="#9C27B0" />
                      <div className="stat-info">
                        <span className="stat-value">{completedRemindersCount}/{weeklyReminders.length}</span>
                        <span className="stat-label">檢核點完成</span>
                      </div>
                    </div>
                  )}
                </div>
              </section>

              {weeklyReminders.length > 0 && (
                <div className="reminders-section">
                  <h2>{selectedWeekOffset === 0 ? '本週檢核點' : `${getWeekDisplayText(selectedWeekOffset)}檢核點`}</h2>
                  <div className="reminders-list">
                    {weeklyReminders.map((reminder, index) => (
                      <div key={index} className="reminder-item" style={{ '--index': index } as React.CSSProperties}>
                        <div className="reminder-header">
                          <div className="reminder-icon">
                            <IconWrapper icon={FaClipboardCheck} size={16} color="#9C27B0" />
                          </div>
                          <div className="reminder-info">
                            <h4>{reminder.title}</h4>
                            <p className="reminder-date">
                              {new Date(reminder.date).toLocaleDateString('zh-TW', { 
                                month: 'numeric', 
                                day: 'numeric',
                                weekday: 'short'
                              })}
                            </p>
                          </div>
                          <div className="reminder-status">
                            {reminder.checkStatus === 'completed' ? (
                              <span className="status-completed">
                                <IconWrapper icon={FaClipboardCheck} size={14} color="#4CAF50" />
                                已完成
                              </span>
                            ) : (
                              <span className="status-pending">
                                未完成
                              </span>
                            )}
                          </div>
                        </div>
                        {reminder.description && (
                          <div className="reminder-description">
                            <strong>檢核方法：</strong>
                            <br />
                            {reminder.description}
                          </div>
                        )}
                        {reminder.checkStatus === 'completed' && reminder.earnedStars && (
                          <div className="reminder-stars">
                            <IconWrapper icon={FaStar} size={14} color="#FFD700" />
                            <span>獲得 {reminder.earnedStars} 顆星星</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="progress-section">
                <h2>{t('reports.taskTypeProgress') || '各類型任務進度'}</h2>
                <div className="progress-bars">
                  {taskTypeProgress.map((type, index) => (
                    <div key={index} className="progress-item">
                      <div className="progress-header">
                        <span>{type.name}</span>
                        <span>{type.total === 0 ? '0%' : `${type.completed}/${type.total} (${type.progress}%)`}</span>
                      </div>
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{
                            width: `${type.progress}%`,
                            backgroundColor: type.color
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 單字挑戰正確率圖表 */}
              <div className="vocabulary-chart-section">
                <h2>{t('reports.weeklyVocabularyAccuracy') || (selectedWeekOffset === 0 ? '週單字挑戰' : `${getWeekDisplayText(selectedWeekOffset)}單字挑戰`)}</h2>
                {/* 固定顯示七天的單字挑戰圖表 */}
                <div className="vocabulary-chart-container">
                    <div className="vocabulary-chart">
                      {weeklyVocabularyData.map((data, index) => {
                        const dayNames = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];
                        const dayName = dayNames[index];

                        return (
                          <div
                            key={index}
                            className="vocabulary-chart-item"
                            style={{ '--index': index } as React.CSSProperties}
                          >
                            <div className="vocabulary-chart-day">{dayName}</div>
                            <div className="vocabulary-chart-bar-container">
                              <div
                                className={`vocabulary-chart-bar ${data.challenged ? 'challenged' : 'not-challenged'}`}
                                style={{
                                  height: data.challenged ? `${Math.max(data.accuracy, 5)}%` : '5%',
                                  backgroundColor: data.challenged ?
                                    (data.accuracy >= 80 ? '#4CAF50' :
                                     data.accuracy >= 60 ? '#FF9800' : '#F44336') : '#E0E0E0'
                                }}
                              >
                                {data.challenged && (
                                  <span className="vocabulary-chart-percentage">{data.accuracy}%</span>
                                )}
                              </div>
                            </div>
                            <div className="vocabulary-chart-status">
                              {data.challenged ? '已挑戰' : '未挑戰'}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    <div className="vocabulary-chart-legend">
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#4CAF50' }}></div>
                        <span>優秀 (≥80%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#FF9800' }}></div>
                        <span>良好 (60-79%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#F44336' }}></div>
                        <span>需加強 (&lt;60%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#E0E0E0' }}></div>
                        <span>未挑戰</span>
                      </div>
                  </div>
                </div>
              </div>

              {/* 睡前檢查表審核結果 */}
              {bedtimeWeeklyStats && (
                <div className="achievements-section bedtime-checklist-section">
                  <h2>🌙 {selectedWeekOffset === 0 ? '本週睡前檢查表審核結果' : `${getWeekDisplayText(selectedWeekOffset)}睡前檢查表審核結果`}</h2>

                  {/* 總體統計 */}
                  <div className="bedtime-overview">
                    <div className="bedtime-stat-card">
                      <div className="stat-icon">📊</div>
                      <div className="stat-content">
                        <div className="stat-value">{bedtimeWeeklyStats.overallCompletionRate}%</div>
                        <div className="stat-label">整體完成率</div>
                      </div>
                    </div>
                    <div className="bedtime-stat-card">
                      <div className="stat-icon">✅</div>
                      <div className="stat-content">
                        <div className="stat-value">{bedtimeWeeklyStats.completedSubItems}/{bedtimeWeeklyStats.totalSubItems}</div>
                        <div className="stat-label">完成小項目</div>
                      </div>
                    </div>
                    <div className="bedtime-stat-card">
                      <div className="stat-icon">👨‍👩‍👧‍👦</div>
                      <div className="stat-content">
                        <div className="stat-value">{bedtimeWeeklyStats.reviewedDays}/{bedtimeWeeklyStats.totalDays}</div>
                        <div className="stat-label">家長審核天數</div>
                      </div>
                    </div>
                  </div>

                  {/* 每日完成率圖表 */}
                  <div className="bedtime-daily-chart">
                    <div style={{ marginBottom: '15px' }}></div>
                    <div className="bedtime-chart-container">
                      {bedtimeWeeklyStats.dailyStats.map((dayStats, index) => {
                        const dayNames = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];
                        const dayName = dayNames[index] || `第${index + 1}天`;

                        return (
                          <div
                            key={dayStats.date}
                            className="bedtime-chart-item"
                            style={{ '--index': index } as React.CSSProperties}
                          >
                            <div className="bedtime-chart-day">{dayName}</div>
                            <div className="bedtime-chart-bar-container">
                              <div
                                className={`bedtime-chart-bar ${dayStats.totalSubItems > 0 ? (dayStats.isReviewed ? 'reviewed' : 'not-reviewed') : 'no-data'}`}
                                style={{
                                  height: dayStats.totalSubItems > 0 ? `${Math.max(dayStats.completionRate, 5)}%` : '5%',
                                  backgroundColor: dayStats.totalSubItems > 0 ?
                                    (dayStats.isReviewed ?
                                      (dayStats.completionRate >= 80 ? '#4CAF50' :
                                       dayStats.completionRate >= 60 ? '#FF9800' : '#F44336') : '#E0E0E0') : '#F5F5F5'
                                }}
                              >
                                {dayStats.totalSubItems > 0 && dayStats.completionRate > 0 && (
                                  <span className="bedtime-chart-percentage">{dayStats.completionRate}%</span>
                                )}
                              </div>
                            </div>
                            <div className="bedtime-chart-details">
                              <div className="bedtime-chart-count">
                                {dayStats.totalSubItems > 0 ? `${dayStats.completedSubItems}/${dayStats.totalSubItems}` : '0/18'}
                              </div>
                              <div className="bedtime-chart-status">
                                {dayStats.totalSubItems > 0 ?
                                  (dayStats.isReviewed ? '已審核' : '未審核') :
                                  '未進行'
                                }
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    <div className="bedtime-chart-legend">
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#4CAF50' }}></div>
                        <span>優秀 (≥80%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#FF9800' }}></div>
                        <span>良好 (60-79%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#F44336' }}></div>
                        <span>需加強 (&lt;60%)</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#E0E0E0' }}></div>
                        <span>未審核</span>
                      </div>
                      <div className="legend-item">
                        <div className="legend-color" style={{ backgroundColor: '#F5F5F5', border: '2px dashed #ccc' }}></div>
                        <span>未進行</span>
                      </div>
                    </div>
                  </div>

                  {/* 各項目進度統計 - 任務進度風格 */}
                  <div className="bedtime-progress-summary">
                    <h3>各項目完成進度</h3>
                    <div className="bedtime-progress-list">
                      {(() => {
                        // 定義睡前檢查表項目的顏色分類
                        const bedtimeItemColors: Record<string, string> = {
                          '整理書包': '#4caf50',    // 綠色 - 學習相關
                          '收拾玩具': '#2196f3',    // 藍色 - 整理相關
                          '分享今日心得': '#ff9800', // 橙色 - 溝通相關
                          '自我反省': '#9c27b0',    // 紫色 - 品格相關
                          '睡前準備': '#795548'     // 棕色 - 生活習慣
                        };

                        const groupedItems = Object.entries(bedtimeWeeklyStats.subItemsSummary).reduce((groups, [subItemId, summary]) => {
                          const mainTitle = summary.mainItemTitle;
                          if (!groups[mainTitle]) {
                            groups[mainTitle] = {
                              icon: summary.mainItemIcon,
                              color: bedtimeItemColors[mainTitle] || '#666',
                              items: []
                            };
                          }
                          groups[mainTitle].items.push({ id: subItemId, ...summary });
                          return groups;
                        }, {} as Record<string, {
                          icon: string;
                          color: string;
                          items: Array<{
                            id: string;
                            title: string;
                            mainItemTitle: string;
                            mainItemIcon: string;
                            completedDays: number;
                            totalDays: number;
                            completionRate: number;
                          }>;
                        }>);

                        return Object.entries(groupedItems).map(([mainTitle, group], groupIndex) => {
                        // 計算主項目的整體完成率
                        const totalPossible = group.items.length * 7; // 小項目數量 × 7天
                        const totalCompleted = group.items.reduce((sum, item) => sum + item.completedDays, 0);
                        const mainCompletionRate = totalPossible > 0 ? Math.round((totalCompleted / totalPossible) * 100) : 0;

                        return (
                          <div key={mainTitle} className="bedtime-progress-item" style={{ '--index': groupIndex } as React.CSSProperties}>
                            {/* 主項目進度條 */}
                            <div
                              className="main-progress-header"
                              onClick={() => {
                                const subItems = document.querySelector(`#subitems-${groupIndex}`);
                                const arrow = document.querySelector(`#arrow-${groupIndex}`);
                                if (subItems && arrow) {
                                  subItems.classList.toggle('expanded');
                                  arrow.classList.toggle('rotated');
                                }
                              }}
                            >
                              <div className="main-progress-info">
                                <span className="main-progress-icon">{group.icon}</span>
                                <span className="main-progress-title">{mainTitle}</span>
                                <span className="main-progress-count">({group.items.length}項)</span>
                              </div>
                              <div className="main-progress-stats">
                                <span className="main-progress-percentage">{mainCompletionRate}%</span>
                                <span id={`arrow-${groupIndex}`} className="expand-arrow">▼</span>
                              </div>
                            </div>

                            <div className="main-progress-bar">
                              <div
                                className="main-progress-fill"
                                style={{
                                  width: `${mainCompletionRate}%`,
                                  backgroundColor: group.color,
                                  opacity: mainCompletionRate >= 80 ? 1 :
                                          mainCompletionRate >= 60 ? 0.8 : 0.6
                                }}
                              ></div>
                            </div>

                            {/* 小項目進度條 - 可展開 */}
                            <div id={`subitems-${groupIndex}`} className="subitems-progress">
                              {group.items.map((item) => (
                                <div key={item.id} className="subitem-progress-row">
                                  <div className="subitem-progress-info">
                                    <span className="subitem-progress-title">{item.title}</span>
                                    <span className="subitem-progress-stats">{item.completedDays}/{item.totalDays}</span>
                                  </div>
                                  <div className="subitem-progress-bar">
                                    <div
                                      className="subitem-progress-fill"
                                      style={{
                                        width: `${item.completionRate}%`,
                                        backgroundColor: group.color,
                                        opacity: item.completionRate >= 80 ? 1 :
                                                item.completionRate >= 60 ? 0.8 : 0.6
                                      }}
                                    ></div>
                                  </div>
                                  <span className="subitem-progress-percentage">{item.completionRate}%</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                        });
                      })()}
                    </div>
                  </div>
                </div>
              )}

              {/* 成就獎勵記錄 */}
              {weeklyAchievementRewards.length > 0 && (
                <div className="achievements-section achievement-rewards-section">
                  <h2>🏆 {selectedWeekOffset === 0 ? '本週成就獎勵' : `${getWeekDisplayText(selectedWeekOffset)}成就獎勵`}</h2>
                  <div className="achievement-rewards-list">
                    {weeklyAchievementRewards.map((reward, index) => (
                      <div key={reward.id} className="achievement-reward-item" style={{ '--index': index } as React.CSSProperties}>
                        <div className="achievement-reward-header">
                          <div className="achievement-reward-icon">
                            <span className="prize-icon">{reward.prize.icon}</span>
                          </div>
                          <div className="achievement-reward-info">
                            <h4>{reward.prize.name}</h4>
                          </div>
                          <div className="achievement-reward-badge">
                            <span className="achievement-badge" style={{ backgroundColor: reward.prize.color }}>
                              成就獎勵
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="achievements-section reward-exchanges-section">
                <h2>{t('reports.weeklyRewardExchanges') || (selectedWeekOffset === 0 ? '本週獎勵兌換紀錄' : `${getWeekDisplayText(selectedWeekOffset)}獎勵兌換紀錄`)}</h2>
                {rewardExchangeStats.length > 0 ? (
                  <div className="reward-exchanges-chart-container" ref={scrollContainerRef}>
                    <div className="reward-exchanges-chart">
                      {rewardExchangeStats.map((stat, index) => {
                        // 圖片組件，處理載入失敗的情況
                        const RewardImage = () => {
                          const [imageSrc, setImageSrc] = React.useState(stat.image || '/gift.png');
                          const [hasError, setHasError] = React.useState(false);

                          const handleImageError = () => {
                            if (!hasError) {
                              setHasError(true);
                              setImageSrc('/gift.png');
                            }
                          };

                          return (
                            <img
                              src={imageSrc}
                              alt={stat.title}
                              onError={handleImageError}
                            />
                          );
                        };

                        return (
                          <div key={index} className="reward-exchange-bar">
                            <div className="reward-exchange-bar-label">
                              <div className="reward-exchange-icon">
                                <RewardImage />
                              </div>
                              <span className="reward-title">{stat.title}</span>
                            </div>
                            <div className="reward-exchange-bar-container">
                              <div
                                className="reward-exchange-bar-fill"
                                style={{
                                  width: `${Math.min(100, stat.count * 20)}%`,
                                  backgroundColor: getRewardCategoryColor(stat.category)
                                }}
                              ></div>
                              <span className="reward-exchange-count">{stat.count}</span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="no-exchanges-message">
                    <p>{t('reports.noRewardExchanges') || (selectedWeekOffset === 0 ? '本週尚無獎勵兌換紀錄' : `${getWeekDisplayText(selectedWeekOffset)}尚無獎勵兌換紀錄`)}</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
  );
};

export default Reports;