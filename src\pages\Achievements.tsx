import React, { useState, useEffect } from 'react';
import { <PERSON>aT<PERSON>hy, FaBook, FaD<PERSON>bbell, FaBroom, FaGraduationCap, FaHeart, FaStar, FaGift, FaTasks, FaBell, FaExclamationTriangle, FaMoon } from 'react-icons/fa';
import IconWrapper from '../components/IconWrapper';
import { useAuth } from '../contexts/AuthContext';
import { useChild } from '../contexts/ChildContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Database } from '../services/Database';
import { isSunday, getCurrentSundayDateString } from '../utils/dateUtils';
import LotteryWheel, { LotteryPrize } from '../components/LotteryWheel';
import '../styles/Achievements.css';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: any;
  color: string;
  taskType: string;
  progress: number;
  maxProgress: number;
  isUnlocked: boolean;
  isRedeemed?: boolean; // 是否已兌換
  level?: number; // 成就等級 (1, 2, 3)
  multiplier?: number; // 目標倍數
}

interface LotteryRecord {
  achievementId: string;
  prize: LotteryPrize;
  timestamp: string;
  date: string;
}

interface DailyLotteryConfig {
  date: string;
  selectedGroupId: string;
  timestamp: string;
}

interface Task {
  id: string;
  title: string;
  type: string;
  status: string;
  date?: string;
}

interface TaskStats {
  [taskType: string]: {
    total: number;
    completed: number;
  };
}

interface TodayTask {
  id: string;
  title: string;
  type: string;
  status: string;
  description?: string;
  points?: number;
  priority?: number;
}

const Achievements: React.FC = () => {
  const { currentUser } = useAuth();
  const { selectedChild } = useChild();
  const { t } = useLanguage();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [taskStats, setTaskStats] = useState<TaskStats>({});
  const [showLotteryWheel, setShowLotteryWheel] = useState(false);
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);
  const [showLotteryMessage, setShowLotteryMessage] = useState(false);
  const [todayTasks, setTodayTasks] = useState<TodayTask[]>([]);
  const [expandedAchievements, setExpandedAchievements] = useState<Set<string>>(new Set());


  // 五組不同的抽獎獎品組合
  const lotteryPrizeGroups: { [key: string]: LotteryPrize[] } = {
    group1: [
      { id: 'apple', name: '蘋果x1', color: '#FF4757', icon: '🍎' },
      { id: 'kiwi_2', name: '奇異果x1', color: '#26de81', icon: '🥝' },
      { id: 'stars_10_1', name: '星星x10', color: '#FFA502', icon: '⭐' },
      { id: 'stars_20_1', name: '星星x20', color: '#FF6B35', icon: '⭐' },
      { id: 'kiwi_1', name: '奇異果x1', color: '#26de81', icon: '🥝' },
      { id: 'mini_cookie_1', name: '迷你餅乾', color: '#B22222', icon: '🍪' },
      { id: 'yogurt_1', name: '優格', color: '#99D9EA', icon: '🥛' },
      { id: 'orange_2', name: '橘子x1', color: '#F28500', icon: '🍊' },
      { id: 'stars_100', name: '星星x100', color: '#8B5CF6', icon: '⭐' },
      { id: 'stars_30_1', name: '星星x30', color: '#FF3838', icon: '⭐' },
      { id: 'banana_1', name: '香蕉x1', color: '#FFD93D', icon: '🍌' },
      { id: 'orange_1', name: '橘子x1', color: '#F28500', icon: '🍊' },
      { id: 'stars_10_3', name: '星星x10', color: '#FFA502', icon: '⭐' }
    ],
    group2: [
      { id: 'mini_cookie', name: '迷你餅乾', color: '#D2691E', icon: '🍪' },
      { id: 'yogurt', name: '優格', color: '#99D9EA', icon: '🥛' },
      { id: 'stars_15_1', name: '星星x15', color: '#FFB347', icon: '⭐' },
      { id: 'stars_25_1', name: '星星x25', color: '#FF7F50', icon: '⭐' },
      { id: 'tomato', name: '番茄x5', color: '#FF6347', icon: '🍅' },
      { id: 'yogurt_1', name: '優格', color: '#99D9EA', icon: '🥛' },
      { id: 'stars_15_2', name: '星星x15', color: '#FFB347', icon: '⭐' },
      { id: 'grape', name: '葡萄x3', color: '#9966CC', icon: '🍇' },
      { id: 'stars_100', name: '星星x100', color: '#9370DB', icon: '⭐' },
      { id: 'stars_15_3', name: '星星x15', color: '#DC143C', icon: '⭐' },
      { id: 'cherry', name: '櫻桃x3', color: '#DC143C', icon: '🍒' },
      { id: 'apple_2', name: '蘋果x1', color: '#FF4757', icon: '🍎' },
      { id: 'stars_25_3', name: '星星x25', color: '#FFB347', icon: '⭐' }
    ],
    group3: [
      { id: 'banana_2', name: '香蕉x1', color: '#FFD93D', icon: '🍌' },
      { id: 'kiwi_3', name: '奇異果x1', color: '#26de81', icon: '🥝' },
      { id: 'stars_10_1', name: '星星x10', color: '#FF8C00', icon: '⭐' },
      { id: 'stars_20_1', name: '星星x20', color: '#FF4500', icon: '⭐' },
      { id: 'orange_3', name: '橘子x1', color: '#F28500', icon: '🍊' },
      { id: 'stars_20_2', name: '星星x20', color: '#FF4500', icon: '⭐' },
      { id: 'yogurt_1', name: '優格', color: '#99D9EA', icon: '🥛' },
      { id: 'grape_2', name: '葡萄x5', color: '#9966CC', icon: '🍇' },
      { id: 'stars_120', name: '星星x120', color: '#4B0082', icon: '⭐' },
      { id: 'mini_cookie', name: '迷你餅乾', color: '#B22222', icon: '🍪' },
      { id: 'cherry_2', name: '櫻桃x3', color: '#DC143C', icon: '🍒' },
      { id: 'tomato_2', name: '番茄x5', color: '#FF6347', icon: '🍅' },
      { id: 'stars_10_3', name: '星星x10', color: '#FF8C00', icon: '⭐' }
    ],
    group4: [
      { id: 'apple_3', name: '蘋果x1', color: '#FF4757', icon: '🍎' },
      { id: 'banana_3', name: '香蕉x1', color: '#FFD93D', icon: '🍌' },
      { id: 'stars_10_1', name: '星星x10', color: '#FF6347', icon: '⭐' },
      { id: 'stars_20_1', name: '星星x20', color: '#FF1493', icon: '⭐' },
      { id: 'orange_4', name: '橘子x1', color: '#F28500', icon: '🍊' },
      { id: 'stars_10_2', name: '星星x10', color: '#FF1493', icon: '⭐' },
      { id: 'mini_cookie', name: '迷你餅乾', color: '#FF6347', icon: '🍪' },
      { id: 'grape_3', name: '葡萄x5', color: '#9966CC', icon: '🍇' },
      { id: 'stars_100', name: '星星x100', color: '#8A2BE2', icon: '⭐' },
      { id: 'stars_30_1', name: '星星x30', color: '#FF0000', icon: '⭐' },
      { id: 'cherry_3', name: '櫻桃x3', color: '#DC143C', icon: '🍒' },
      { id: 'kiwi_4', name: '奇異果x1', color: '#26de81', icon: '🥝' },
      { id: 'stars_10_3', name: '星星x10', color: '#FF6347', icon: '⭐' }
    ],
    group5: [
      { id: 'tomato_3', name: '番茄x5', color: '#FF6347', icon: '🍅' },
      { id: 'apple_4', name: '蘋果x1', color: '#FF4757', icon: '🍎' },
      { id: 'stars_20_1', name: '星星x20', color: '#FF7F00', icon: '⭐' },
      { id: 'mini_cookie', name: '迷你餅乾', color: '#FF69B4', icon: '🍪' },
      { id: 'banana_4', name: '香蕉x1', color: '#FFD93D', icon: '🍌' },
      { id: 'stars_10_2', name: '星星x10', color: '#FF69B4', icon: '⭐' },
      { id: 'stars_10_3', name: '星星x10', color: '#FF7F00', icon: '⭐' },
      { id: 'orange_5', name: '橘子x1', color: '#F28500', icon: '🍊' },
      { id: 'stars_150', name: '星星x150', color: '#6A5ACD', icon: '⭐' },
      { id: 'mini_cookie_2', name: '迷你餅乾', color: '#FF4500', icon: '🍪' },
      { id: 'grape_4', name: '葡萄x5', color: '#9966CC', icon: '🍇' },
      { id: 'yogurt_1', name: '優格', color: '#99D9EA', icon: '🥛' },
      { id: 'stars_20_3', name: '星星x20', color: '#FF7F00', icon: '⭐' }
    ]
  };

  // 當前使用的抽獎獎品（從每日選擇的組合中取得）
  const [currentLotteryPrizes, setCurrentLotteryPrizes] = useState<LotteryPrize[]>(lotteryPrizeGroups.group1);

  // 定義成就配置（排除額外任務）- 每種任務類型有3個等級
  const achievementConfigs = [
    // 作業類成就 (3個等級)
    {
      id: 'homework_beginner',
      name: t('achievements.homeworkBeginner'),
      description: t('achievements.homeworkBeginnerDesc'),
      icon: FaBook,
      color: '#4caf50',
      taskType: 'homework',
      level: 1,
      multiplier: 1
    },
    {
      id: 'homework_expert',
      name: t('achievements.homeworkExpert'),
      description: t('achievements.homeworkExpertDesc'),
      icon: FaBook,
      color: '#4caf50',
      taskType: 'homework',
      level: 2,
      multiplier: 2
    },
    {
      id: 'homework_master',
      name: t('achievements.homeworkMaster'),
      description: t('achievements.homeworkMasterDesc'),
      icon: FaBook,
      color: '#4caf50',
      taskType: 'homework',
      level: 3,
      multiplier: 3
    },
    // 運動類成就 (3個等級)
    {
      id: 'exercise_beginner',
      name: t('achievements.exerciseBeginner'),
      description: t('achievements.exerciseBeginnerDesc'),
      icon: FaDumbbell,
      color: '#2196f3',
      taskType: 'exercise',
      level: 1,
      multiplier: 1
    },
    {
      id: 'exercise_expert',
      name: t('achievements.exerciseExpert'),
      description: t('achievements.exerciseExpertDesc'),
      icon: FaDumbbell,
      color: '#2196f3',
      taskType: 'exercise',
      level: 2,
      multiplier: 2
    },
    {
      id: 'exercise_master',
      name: t('achievements.exerciseMaster'),
      description: t('achievements.exerciseMasterDesc'),
      icon: FaDumbbell,
      color: '#2196f3',
      taskType: 'exercise',
      level: 3,
      multiplier: 3
    },
    // 家務類成就 (3個等級)
    {
      id: 'housework_beginner',
      name: t('achievements.houseworkBeginner'),
      description: t('achievements.houseworkBeginnerDesc'),
      icon: FaBroom,
      color: '#ff9800',
      taskType: 'housework',
      level: 1,
      multiplier: 1
    },
    {
      id: 'housework_expert',
      name: t('achievements.houseworkExpert'),
      description: t('achievements.houseworkExpertDesc'),
      icon: FaBroom,
      color: '#ff9800',
      taskType: 'housework',
      level: 2,
      multiplier: 2
    },
    {
      id: 'housework_master',
      name: t('achievements.houseworkMaster'),
      description: t('achievements.houseworkMasterDesc'),
      icon: FaBroom,
      color: '#ff9800',
      taskType: 'housework',
      level: 3,
      multiplier: 3
    },
    // 學習類成就 (3個等級)
    {
      id: 'study_beginner',
      name: t('achievements.studyBeginner'),
      description: t('achievements.studyBeginnerDesc'),
      icon: FaGraduationCap,
      color: '#9c27b0',
      taskType: 'study',
      level: 1,
      multiplier: 1
    },
    {
      id: 'study_expert',
      name: t('achievements.studyExpert'),
      description: t('achievements.studyExpertDesc'),
      icon: FaGraduationCap,
      color: '#9c27b0',
      taskType: 'study',
      level: 2,
      multiplier: 2
    },
    {
      id: 'study_master',
      name: t('achievements.studyMaster'),
      description: t('achievements.studyMasterDesc'),
      icon: FaGraduationCap,
      color: '#9c27b0',
      taskType: 'study',
      level: 3,
      multiplier: 3
    },
    // 習慣類成就 (3個等級)
    {
      id: 'habit_beginner',
      name: t('achievements.habitBeginner'),
      description: t('achievements.habitBeginnerDesc'),
      icon: FaHeart,
      color: '#795548',
      taskType: 'habit',
      level: 1,
      multiplier: 1
    },
    {
      id: 'habit_expert',
      name: t('achievements.habitExpert'),
      description: t('achievements.habitExpertDesc'),
      icon: FaHeart,
      color: '#795548',
      taskType: 'habit',
      level: 2,
      multiplier: 2
    },
    {
      id: 'habit_master',
      name: t('achievements.habitMaster'),
      description: t('achievements.habitMasterDesc'),
      icon: FaHeart,
      color: '#795548',
      taskType: 'habit',
      level: 3,
      multiplier: 3
    },
    // 總任務完成成就 (3個等級)
    {
      id: 'total_beginner',
      name: t('achievements.totalBeginner'),
      description: t('achievements.totalBeginnerDesc'),
      icon: FaTasks,
      color: '#607d8b',
      taskType: 'total',
      level: 1,
      multiplier: 1
    },
    {
      id: 'total_expert',
      name: t('achievements.totalExpert'),
      description: t('achievements.totalExpertDesc'),
      icon: FaTasks,
      color: '#607d8b',
      taskType: 'total',
      level: 2,
      multiplier: 2
    },
    {
      id: 'total_master',
      name: t('achievements.totalMaster'),
      description: t('achievements.totalMasterDesc'),
      icon: FaTasks,
      color: '#607d8b',
      taskType: 'total',
      level: 3,
      multiplier: 3
    },

    // 睡前檢查表成就 (3個等級)
    {
      id: 'bedtime_beginner',
      name: t('achievements.bedtimeBeginner'),
      description: t('achievements.bedtimeBeginnerDesc'),
      icon: FaMoon,
      color: '#5c6bc0',
      taskType: 'bedtime',
      level: 1,
      multiplier: 1
    },
    {
      id: 'bedtime_expert',
      name: t('achievements.bedtimeExpert'),
      description: t('achievements.bedtimeExpertDesc'),
      icon: FaMoon,
      color: '#5c6bc0',
      taskType: 'bedtime',
      level: 2,
      multiplier: 2
    },
    {
      id: 'bedtime_master',
      name: t('achievements.bedtimeMaster'),
      description: t('achievements.bedtimeMasterDesc'),
      icon: FaMoon,
      color: '#5c6bc0',
      taskType: 'bedtime',
      level: 3,
      multiplier: 3
    }
  ];

  // 獲取當天日期字符串
  const getTodayDateString = (): string => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 獲取或選擇當天的抽獎組合
  const getDailyLotteryGroup = async (): Promise<string> => {
    if (!currentUser) return 'group1';

    try {
      const todayDate = getTodayDateString();
      console.log('獲取當天抽獎組合，日期:', todayDate);

      // 先檢查資料庫中是否已有當天的抽獎組合配置（存在 userId 下）
      const existingConfigs = await Database.queryDocs<DailyLotteryConfig>(
        'dailyLotteryConfig', 
        [], 
        undefined, 
        undefined, 
        undefined, 
        undefined, 
        undefined, 
        todayDate
      );

      if (existingConfigs.length > 0) {
        console.log('找到現有的抽獎組合配置:', existingConfigs[0]);
        return existingConfigs[0].selectedGroupId;
      }

      // 如果沒有現有配置，隨機選擇一組
      const groupIds = Object.keys(lotteryPrizeGroups);
      const randomIndex = Math.floor(Math.random() * groupIds.length);
      const selectedGroupId = groupIds[randomIndex];

      console.log('隨機選擇的抽獎組合:', selectedGroupId);

      // 使用固定的文檔 ID 來避免重複寫入（一天只能有一個配置）
      const docId = `lottery_${todayDate}`;

      // 儲存選擇的組合到資料庫（存在 userId 下）
      const lotteryConfig: DailyLotteryConfig = {
        date: todayDate,
        selectedGroupId,
        timestamp: new Date().toISOString()
      };

      // 使用 setDoc 而不是 addDoc，確保每天只有一個配置
      await Database.setDoc('dailyLotteryConfig', docId, lotteryConfig, undefined, undefined, todayDate);
      console.log('抽獎組合配置已儲存:', lotteryConfig);

      return selectedGroupId;
    } catch (error) {
      console.error('獲取每日抽獎組合失敗:', error);
      return 'group1'; // 發生錯誤時返回預設組合
    }
  };

  // 獲取本週一的日期
  const getMonday = (date: Date): Date => {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 調整星期日
    return new Date(date.setDate(diff));
  };

  // 生成本週日期範圍（週一到週日）
  const getCurrentWeekDateRange = (): string[] => {
    const today = new Date();
    const monday = getMonday(new Date(today));
    const dateRange: string[] = [];

    // 生成從週一到週日的日期
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(monday);
      currentDate.setDate(monday.getDate() + i);
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      dateRange.push(`${year}-${month}-${day}`);
    }

    return dateRange;
  };



  // 獲取當天任務數據
  const loadTodayTasks = async () => {
    if (!currentUser || !selectedChild) {
      setTodayTasks([]);
      return;
    }

    try {
      const today = new Date().toISOString().split('T')[0];
      const tasks = await Database.queryDocs<TodayTask>('tasks', [], undefined, undefined, undefined, undefined, selectedChild.id, today);
      setTodayTasks(tasks);
    } catch (error) {
      console.error('載入當天任務失敗:', error);
      setTodayTasks([]);
    }
  };

  // 載入當週睡前檢查表數據並統計小項目完成數量
  const loadBedtimeChecklistStats = async (): Promise<number> => {
    if (!currentUser || !selectedChild) return 0;

    try {
      const today = new Date();
      // 使用與系統其他地方一致的週一為一週開始邏輯
      const monday = getMonday(new Date(today));

      console.log(`睡前檢查表統計 - 今天: ${today.toISOString().split('T')[0]}, 本週一: ${monday.toISOString().split('T')[0]}`);

      let totalCompletedSubItems = 0;

      // 生成本週日期範圍用於日誌
      const weekDates = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(monday);
        date.setDate(monday.getDate() + i);
        weekDates.push(date.toISOString().split('T')[0]);
      }
      console.log(`睡前檢查表統計 - 查詢日期範圍: ${weekDates.join(', ')}`);

      // 載入當週每一天的睡前檢查表數據（週一到週日）
      for (let i = 0; i < 7; i++) {
        const checkDate = new Date(monday);
        checkDate.setDate(monday.getDate() + i);
        const dateString = checkDate.toISOString().split('T')[0];

        try {
          const bedtimeData = await Database.getDoc('bedtimeChecklist', 'checklist', currentUser.uid, selectedChild.id, dateString);

          // 只計算家長審核過的記錄
          if (bedtimeData && bedtimeData.checklistData && bedtimeData.checklistData.status === 'reviewed' && bedtimeData.checklistData.parentReviewedAt) {
            const items = bedtimeData.checklistData.items || [];

            // 統計所有小項目的完成數量
            items.forEach((item: any) => {
              if (item.subItems && Array.isArray(item.subItems)) {
                item.subItems.forEach((subItem: any) => {
                  if (subItem.completed) {
                    totalCompletedSubItems++;
                  }
                });
              }
            });
          }
        } catch (error) {
          console.error(`載入 ${dateString} 睡前檢查表數據失敗:`, error);
        }
      }

      console.log(`當週睡前檢查表小項目完成總數: ${totalCompletedSubItems}`);
      return totalCompletedSubItems;
    } catch (error) {
      console.error('載入睡前檢查表統計數據失敗:', error);
      return 0;
    }
  };

  // 載入任務統計數據（以週為單位）- 並行載入優化版本
  const loadTaskStats = async () => {
    if (!currentUser) return;

    // 載入當天的抽獎組合
    try {
      const selectedGroupId = await getDailyLotteryGroup();
      const selectedPrizes = lotteryPrizeGroups[selectedGroupId] || lotteryPrizeGroups.group1;
      setCurrentLotteryPrizes(selectedPrizes);
      console.log('設定當天抽獎組合:', selectedGroupId, selectedPrizes);
    } catch (error) {
      console.error('載入當天抽獎組合失敗:', error);
      setCurrentLotteryPrizes(lotteryPrizeGroups.group1);
    }

    // 載入當天任務
    await loadTodayTasks();

    // 如果没有选择儿童，显示空的成就数据
    if (!selectedChild) {
      try {
        setIsLoading(true);
        
        // 初始化空的统计数据
        const stats: TaskStats = {};
        const taskTypes = new Set<string>();
        achievementConfigs.forEach(config => {
          if (!taskTypes.has(config.taskType)) {
            stats[config.taskType] = { total: 0, completed: 0 };
            taskTypes.add(config.taskType);
          }
        });

        setTaskStats(stats);

        // 生成空的成就数据（所有进度都是0）
        const achievementData = achievementConfigs.map(config => {
          let weeklyTarget = 0;

          if (config.taskType === 'total') {
            // 总任务完成成就
            const baseTarget = 20;
            weeklyTarget = baseTarget * config.multiplier;
            if (config.level === 2) weeklyTarget = 50;
            if (config.level === 3) weeklyTarget = 100;
          } else {
            // 特定类型任务成就
            let baseWeeklyTarget = 10;
            switch (config.taskType) {
              case 'homework':
                baseWeeklyTarget = 5;
                break;
              case 'exercise':
                baseWeeklyTarget = 5;
                break;
              case 'housework':
                baseWeeklyTarget = 5;
                break;
              case 'study':
                baseWeeklyTarget = 15;
                break;
              case 'habit':
                baseWeeklyTarget = 10;
                break;
              case 'bedtime':
                baseWeeklyTarget = 20;
                break;
            }

            if (config.taskType === 'bedtime') {
              // 睡前檢查表成就使用固定目標：20、50、100
              if (config.level === 1) weeklyTarget = 20;
              else if (config.level === 2) weeklyTarget = 50;
              else if (config.level === 3) weeklyTarget = 100;
            } else {
              weeklyTarget = baseWeeklyTarget * config.multiplier;
            }
          }

          return {
            id: config.id,
            name: config.name,
            description: config.description,
            icon: config.icon,
            color: config.color,
            taskType: config.taskType,
            progress: 0, // 没有儿童时进度为0
            maxProgress: weeklyTarget,
            isUnlocked: false, // 没有儿童时都不解锁
            isRedeemed: false // 没有儿童时都未兑换
          };
        });

        setAchievements(achievementData);
      } catch (error) {
        console.error('载入空成就数据失败:', error);
      } finally {
        setIsLoading(false);
      }
      return;
    }

    try {
      setIsLoading(true);

      // 獲取本週日期範圍（週一到週日）
      const weekDateRange = getCurrentWeekDateRange();
      console.log('本週日期範圍:', weekDateRange);

      // 獲取週日日期用於查詢兌換記錄
      const sundayDate = getCurrentSundayDateString();
      console.log('查詢兌換記錄的週日日期:', sundayDate);

      // 初始化統計 - 確保每種任務類型只初始化一次
      const stats: TaskStats = {};
      const taskTypes = new Set<string>();
      achievementConfigs.forEach(config => {
        if (!taskTypes.has(config.taskType)) {
          stats[config.taskType] = { total: 0, completed: 0 };
          taskTypes.add(config.taskType);
        }
      });

      // 並行載入兌換記錄和所有日期的任務數據
      const [lotteryRecords, ...taskDataArrays] = await Promise.all([
        // 載入兌換記錄
        Database.queryDocs<LotteryRecord>('achievements', [], undefined, undefined, undefined, undefined, selectedChild.id, sundayDate),
        // 並行載入所有日期的任務數據
        ...weekDateRange.map(date =>
          Database.queryDocs<Task>('tasks', [], undefined, undefined, undefined, undefined, selectedChild.id, date)
            .then(tasks => tasks.filter(task => task.type !== 'extra'))
            .catch(error => {
              console.error(`獲取日期 ${date} 的任務失敗:`, error);
              return [];
            })
        )
      ]);

      console.log('找到的兌換記錄:', lotteryRecords);

      const currentRedeemedSet = new Set<string>();
      lotteryRecords.forEach(record => {
        currentRedeemedSet.add(record.achievementId);
      });

      console.log('當前已兌換的成就ID:', Array.from(currentRedeemedSet));

      // 合併所有任務數據
      const allTasks: Task[] = taskDataArrays.flat();

      console.log('本週任務總數:', allTasks.length);

      // 統計各類型任務
      allTasks.forEach((task: Task) => {
        if (stats[task.type]) {
          stats[task.type].total += 1;
          if (task.status === 'completed') {
            stats[task.type].completed += 1;
          }
        }
      });

      // 載入並統計睡前檢查表數據
      const bedtimeCompletedCount = await loadBedtimeChecklistStats();
      if (stats['bedtime']) {
        stats['bedtime'].completed = bedtimeCompletedCount;
        // 睡前檢查表沒有總數概念，因為是以小項目為單位計算
        stats['bedtime'].total = bedtimeCompletedCount;
      }

      setTaskStats(stats);

      // 計算總完成任務數
      const totalCompletedTasks = Object.values(stats).reduce((sum, stat) => sum + stat.completed, 0);

      // 生成成就數據
      console.log('生成成就數據時的 currentRedeemedSet:', Array.from(currentRedeemedSet));
      const achievementData = achievementConfigs.map(config => {
        let progress = 0;
        let weeklyTarget = 0;

        if (config.taskType === 'total') {
          // 總任務完成成就
          const baseTarget = 20; // 基礎目標：20個任務
          weeklyTarget = baseTarget * config.multiplier; // 20, 40, 60 -> 修正為 20, 50, 100
          if (config.level === 2) weeklyTarget = 50; // 第二級特別設為50
          if (config.level === 3) weeklyTarget = 100; // 第三級特別設為100
          progress = Math.min(totalCompletedTasks, weeklyTarget);
        } else {
          // 特定類型任務成就
          const stat = stats[config.taskType] || { total: 0, completed: 0 };

          // 週期性成就目標：每週完成該類型任務的數量（固定分母）
          // 根據任務類型設定不同的基礎週目標
          let baseWeeklyTarget = 10;
          switch (config.taskType) {
            case 'homework':
              baseWeeklyTarget = 5;
              break;
            case 'exercise':
              baseWeeklyTarget = 5;
              break;
            case 'housework':
              baseWeeklyTarget = 5;
              break;
            case 'study':
              baseWeeklyTarget = 15;
              break;
            case 'habit':
              baseWeeklyTarget = 10;
              break;
            case 'bedtime':
              baseWeeklyTarget = 20; // 睡前檢查表基礎目標：20個小項目
              break;
          }

          // 根據成就等級計算實際目標
          if (config.taskType === 'bedtime') {
            // 睡前檢查表成就使用固定目標：20、50、100
            if (config.level === 1) weeklyTarget = 20;
            else if (config.level === 2) weeklyTarget = 50;
            else if (config.level === 3) weeklyTarget = 100;
          } else {
            // 其他任務類型使用基礎目標 × 倍數
            weeklyTarget = baseWeeklyTarget * config.multiplier;
          }

          // 分子不能超過分母
          progress = Math.min(stat.completed, weeklyTarget);
        }

        const maxProgress = weeklyTarget; // 固定使用週目標作為分母
        const isRedeemed = currentRedeemedSet.has(config.id);

        console.log(`成就 ${config.id}: type=${config.taskType}, level=${config.level}, multiplier=${config.multiplier}, target=${weeklyTarget}, progress=${progress}, totalCompleted=${totalCompletedTasks}, isRedeemed=${isRedeemed}`);

        return {
          id: config.id,
          name: config.name,
          description: config.description,
          icon: config.icon,
          color: config.color,
          taskType: config.taskType,
          progress,
          maxProgress,
          isUnlocked: progress >= weeklyTarget, // 達到週目標即解鎖
          isRedeemed // 是否已兌換
        };
      });

      console.log('最終生成的成就數據:', achievementData);
      setAchievements(achievementData);
    } catch (error) {
      console.error('載入成就數據失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 計算快達標的成就（剩餘3個以內同類型任務）
  const getNearCompletionAchievements = () => {
    const nearCompletion: Array<{
      achievement: Achievement;
      remaining: number;
      taskTypeName: string;
      suggestedTasks: TodayTask[];
    }> = [];

    achievements.forEach(achievement => {
      if (!achievement.isUnlocked && !achievement.isRedeemed && achievement.taskType !== 'bedtime') {
        const remaining = achievement.maxProgress - achievement.progress;
        if (remaining > 0 && remaining <= 3) {
          // 獲取任務類型的中文名稱
          let taskTypeName = '';
          switch (achievement.taskType) {
            case 'homework':
              taskTypeName = t('taskType.homework');
              break;
            case 'exercise':
              taskTypeName = t('taskType.exercise');
              break;
            case 'housework':
              taskTypeName = t('taskType.housework');
              break;
            case 'study':
              taskTypeName = t('taskType.study');
              break;
            case 'habit':
              taskTypeName = t('taskType.habit');
              break;
            case 'total':
              taskTypeName = '任務';
              break;
            default:
              taskTypeName = achievement.taskType;
          }

          // 獲取當天對應類型的未完成任務
          let suggestedTasks: TodayTask[] = [];
          if (achievement.taskType !== 'total') {
            suggestedTasks = todayTasks.filter(task =>
              task.type === achievement.taskType &&
              task.status !== 'completed'
            ).slice(0, 3); // 最多顯示3個建議任務
          } else {
            // 對於總任務成就，顯示所有類型的未完成任務
            suggestedTasks = todayTasks.filter(task =>
              task.status !== 'completed'
            ).slice(0, 3);
          }

          nearCompletion.push({
            achievement,
            remaining,
            taskTypeName,
            suggestedTasks
          });
        }
      }
    });

    return nearCompletion;
  };

  // 切換成就展開狀態
  const toggleAchievementExpansion = (achievementId: string) => {
    setExpandedAchievements(prev => {
      const newSet = new Set(prev);
      if (newSet.has(achievementId)) {
        newSet.delete(achievementId);
      } else {
        newSet.add(achievementId);
      }
      return newSet;
    });
  };

  // 處理成就點擊
  const handleAchievementClick = (achievement: Achievement) => {
    if (!achievement.isUnlocked) return;

    if (isSunday()) {
      // 週日可以抽獎
      if (!achievement.isRedeemed) {
        setSelectedAchievement(achievement);
        setShowLotteryWheel(true);
      }
    } else {
      // 非週日時，顯示抽獎提示訊息
      if (!achievement.isRedeemed) {
        setShowLotteryMessage(true);
      }
    }
  };

  // 處理抽獎結果（立即寫入資料庫）
  const handlePrizeWon = async (prize: LotteryPrize) => {
    if (!currentUser || !selectedChild || !selectedAchievement) return;

    try {
      const sundayDate = getCurrentSundayDateString();
      const lotteryRecord: LotteryRecord = {
        achievementId: selectedAchievement.id,
        prize,
        timestamp: new Date().toISOString(),
        date: sundayDate
      };

      // 立即儲存抽獎記錄到資料庫
      await Database.addDoc('achievements', lotteryRecord, undefined, selectedChild.id, sundayDate);

      console.log('抽獎記錄已儲存:', lotteryRecord);

      // 檢查是否為星星獎勵，如果是則累計到 totalstars
      if (prize.id.startsWith('stars_')) {
        // 從獎品 ID 解析星星數量 (例如: stars_10_1 -> 10, stars_20_1 -> 20)
        const starsMatch = prize.id.match(/stars_(\d+)/);
        if (starsMatch) {
          const starsAmount = parseInt(starsMatch[1], 10);
          console.log(`獲得星星獎勵: ${starsAmount} 顆星星`);

          // 獲取當前總星星數
          const totalStarsData = await Database.getDoc('totalStars', 'stars', undefined, selectedChild.id);
          const currentTotalStars = totalStarsData?.stars || 0;

          // 計算新的總星星數
          const newTotalStars = currentTotalStars + starsAmount;

          // 更新總星星數到資料庫
          await Database.setDoc('totalStars', 'stars', {
            stars: newTotalStars,
            lastUpdated: new Date().toISOString()
          }, undefined, selectedChild.id);

          console.log(`總星星數已更新：從 ${currentTotalStars} 增加到 ${newTotalStars}，新增 ${starsAmount} 顆星星`);
        }
      }
    } catch (error) {
      console.error('儲存抽獎記錄或更新星星數失敗:', error);
    }
  };

  // 處理輪盤直接關閉（不重新載入數據）
  const handleLotteryClose = () => {
    setShowLotteryWheel(false);
  };

  // 處理獎品確認後關閉（重新載入成就數據）
  const handlePrizeConfirmed = async () => {
    setShowLotteryWheel(false);
    // 重新載入成就數據以更新UI
    await loadTaskStats();
  };

  // 獲取成就徽章文字
  const getAchievementBadgeText = (achievement: Achievement) => {
    if (achievement.isRedeemed) {
      return '已兌換';
    }
    return t('achievements.unlocked');
  };

  useEffect(() => {
    loadTaskStats();
  }, [currentUser, selectedChild]);

  // 計算進度百分比
  const getProgressPercentage = (progress: number, maxProgress: number) => {
    return Math.min((progress / maxProgress) * 100, 100);
  };

  // 獲取成就等級
  const getAchievementLevel = (progress: number, maxProgress: number) => {
    const percentage = getProgressPercentage(progress, maxProgress);
    if (percentage >= 100) return t('achievements.level.master');
    if (percentage >= 75) return t('achievements.level.expert');
    if (percentage >= 50) return t('achievements.level.skilled');
    if (percentage >= 25) return t('achievements.level.beginner');
    return t('achievements.level.novice');
  };

  if (isLoading) {
    return (
      <div className="achievements-container">
        <div className="achievements-loading">
          <div className="loading-spinner"></div>
          <p>{t('achievements.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="achievements-container">
      {/* 標題 */}
      <div className="achievements-title-overlay">
        <div className="achievements-header-content">
          <IconWrapper icon={FaTrophy} className="achievements-icon" />
          <h1 className="achievements-title">{t('achievements.title')}</h1>
        </div>
      </div>

      {/* 快達標提醒 */}
      {(() => {
        const nearCompletionAchievements = getNearCompletionAchievements();
        if (nearCompletionAchievements.length > 0) {
          return (
            <div className="near-completion-reminder">
              <div className="reminder-header">
                <IconWrapper icon={FaBell} className="reminder-icon" />
                <h3>快達標提醒</h3>
                <IconWrapper icon={FaExclamationTriangle} className="reminder-warning-icon" />
              </div>
              <div className="reminder-content">
                <p className="reminder-description">以下成就即將達標，加油完成吧！</p>
                <div className="reminder-achievements">
                  {nearCompletionAchievements.map(({ achievement, remaining, taskTypeName, suggestedTasks }) => {
                    const isExpanded = expandedAchievements.has(achievement.id);
                    const hasSuggestedTasks = suggestedTasks.length > 0;

                    return (
                      <div key={achievement.id} className="reminder-achievement-item">
                        <div className="reminder-achievement-main" onClick={() => hasSuggestedTasks && toggleAchievementExpansion(achievement.id)}>
                          <div className="reminder-achievement-icon">
                            <IconWrapper icon={achievement.icon} style={{ color: achievement.color }} />
                          </div>
                          <div className="reminder-achievement-info">
                            <h4>{achievement.name}</h4>
                            <p>還需完成 <strong>{remaining}</strong> 個<strong>{taskTypeName}</strong>任務</p>
                            {hasSuggestedTasks && (
                              <p className="suggested-tasks-hint">
                                今日有 {suggestedTasks.length} 個相關任務可完成
                              </p>
                            )}
                          </div>
                          <div className="reminder-achievement-actions">
                            <div className="reminder-achievement-progress">
                              <span className="progress-text">{achievement.progress}/{achievement.maxProgress}</span>
                            </div>
                            {hasSuggestedTasks && (
                              <div className={`expand-button ${isExpanded ? 'expanded' : ''}`}>
                                <IconWrapper icon={FaBook} />
                              </div>
                            )}
                          </div>
                        </div>

                        {hasSuggestedTasks && isExpanded && (
                          <div className="suggested-tasks-container">
                            <h5>建議完成的任務：</h5>
                            <div className="suggested-tasks-list">
                              {suggestedTasks.map(task => (
                                <div key={task.id} className="suggested-task-item">
                                  <div className="task-status-indicator">
                                    <div className={`status-dot ${task.status}`}></div>
                                  </div>
                                  <div className="task-info">
                                    <h6>{task.title}</h6>
                                    {task.description && (
                                      <p className="task-description">{task.description}</p>
                                    )}
                                    <div className="task-meta">
                                      <span className={`task-status ${task.status}`}>
                                        {task.status === 'not_started' && '未開始'}
                                        {task.status === 'in_progress' && '進行中'}
                                        {task.status === 'pending_review' && '待審核'}
                                        {task.status === 'completed' && '已完成'}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          );
        }
        return null;
      })()}

      {/* 成就統計概覽 */}
      <div className="achievements-overview">
        <div className="overview-card">
          <IconWrapper icon={FaStar} className="overview-icon" />
          <div className="overview-content">
            <h3>{t('achievements.unlockedAchievements')}</h3>
            <p>{achievements.filter(a => a.isUnlocked).length} / {achievements.length}</p>
          </div>
        </div>
        <div className="overview-card">
          <IconWrapper icon={FaTrophy} className="overview-icon" />
          <div className="overview-content">
            <h3>本週完成任務</h3>
            <p>{Object.values(taskStats).reduce((sum, stat) => sum + stat.completed, 0)}</p>
          </div>
        </div>
        <div className="overview-card">
          <IconWrapper icon={FaBook} className="overview-icon" />
          <div className="overview-content">
            <h3>本週總任務</h3>
            <p>{Object.values(taskStats).reduce((sum, stat) => sum + stat.total, 0)}</p>
          </div>
        </div>
      </div>

      {/* 成就列表 */}
      <div className="achievements-grid">
        {achievements.map(achievement => (
          <div
            key={achievement.id}
            className={`achievement-card ${achievement.isUnlocked ? 'unlocked' : 'locked'}`}
            onClick={() => handleAchievementClick(achievement)}
            style={{
              cursor: achievement.isUnlocked && !achievement.isRedeemed ? 'pointer' : 'default'
            }}
          >
            <div className="achievement-header">
              <div
                className="achievement-icon-container"
                style={{ backgroundColor: achievement.color }}
              >
                <IconWrapper
                  icon={achievement.icon}
                  className="achievement-icon"
                />
              </div>
              <div className="achievement-info">
                <h3 className="achievement-name">{achievement.name}</h3>
                <p className="achievement-description">{achievement.description}</p>
              </div>
            </div>

            <div className="achievement-progress">
              <div className="progress-info">
                <span className="progress-text">
                  {achievement.progress} / {achievement.maxProgress}
                </span>
                <span className="progress-level">
                  {getAchievementLevel(achievement.progress, achievement.maxProgress)}
                </span>
              </div>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{
                    width: `${getProgressPercentage(achievement.progress, achievement.maxProgress)}%`,
                    backgroundColor: achievement.color
                  }}
                />
              </div>
            </div>

            {achievement.isUnlocked && (
              <div className={`achievement-badge ${achievement.isRedeemed ? 'redeemed' : ''}`}>
                {achievement.isRedeemed ? (
                  <>
                    <IconWrapper icon={FaGift} className="badge-icon" />
                    <span>{getAchievementBadgeText(achievement)}</span>
                  </>
                ) : isSunday() ? (
                  <>
                    <IconWrapper icon={FaGift} className="badge-icon lottery-available" />
                    <span>可抽獎</span>
                  </>
                ) : (
                  <>
                    <IconWrapper icon={FaTrophy} className="badge-icon" />
                    <span>{getAchievementBadgeText(achievement)}</span>
                  </>
                )}
              </div>
            )}

          </div>
        ))}
      </div>

      {/* 抽獎輪盤 */}
      <LotteryWheel
        isOpen={showLotteryWheel}
        onClose={handleLotteryClose}
        onPrizeWon={handlePrizeWon}
        onPrizeConfirmed={handlePrizeConfirmed}
        prizes={currentLotteryPrizes}
      />

      {/* 抽獎提示訊息視窗 */}
      {showLotteryMessage && (
        <div className="lottery-message-overlay" onClick={() => setShowLotteryMessage(false)}>
          <div className="lottery-message-container" onClick={(e) => e.stopPropagation()}>
            <div className="lottery-message-header">
              <h3>抽獎提示</h3>
              <button
                className="lottery-message-close"
                onClick={() => setShowLotteryMessage(false)}
              >
                ×
              </button>
            </div>
            <div className="lottery-message-content">
              <p>每週日可以進行抽獎</p>
              <p>請在週日時點擊已解鎖的成就來參與抽獎活動！</p>
            </div>
            <div className="lottery-message-actions">
              <button
                className="lottery-message-button"
                onClick={() => setShowLotteryMessage(false)}
              >
                我知道了
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Achievements;
